package com.mlc.workflow.core.editor.structure;

import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import com.mlc.workflow.core.editor.model.canvas.ApprovalNode;
import com.mlc.workflow.core.editor.model.canvas.ApprovalProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FlowNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.WriteNode;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.operation.BranchOperations;
import com.mlc.workflow.core.editor.structure.operation.GatewayOperations;
import com.mlc.workflow.core.editor.structure.operation.NodeOperations;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

/**
 * 工作流编辑器集成测试
 * 验证工作流编辑器的核心功能、EndOwner管理机制和边界场景
 */
@Slf4j
@SpringBootTest
@ContextConfiguration(classes = WorkflowEditorTestConfig.class)
class WorkflowEditorIntegrationTest {

    private WorkflowEditor workflowEditor;
    private EndOwnerManager endOwnerManager;
    private ProcessNode testProcessNode;

    @BeforeEach
    void setUp() {
        // 初始化工作流编辑器及其依赖组件
        WorkflowValidator workflowValidator = new WorkflowValidator();
        NodeBatchExecutor nodeBatchExecutor = new NodeBatchExecutor(workflowValidator);
        endOwnerManager = new EndOwnerManager();
        AutoWireStrategy autoWireStrategy = new AutoWireStrategy(endOwnerManager);
        GatewayOperations gatewayOperations = new GatewayOperations(nodeBatchExecutor, autoWireStrategy);
        BranchOperations branchOperations = new BranchOperations(nodeBatchExecutor, autoWireStrategy, gatewayOperations);
        NodeOperations nodeOperations = new NodeOperations(nodeBatchExecutor, autoWireStrategy);

        workflowEditor = new WorkflowEditor(gatewayOperations, branchOperations, nodeOperations, nodeBatchExecutor, workflowValidator);
        testProcessNode = createBasicWorkflow();
    }

    /**
     * 创建基础工作流：开始 -> 审批 -> 结束
     */
    private ProcessNode createBasicWorkflow() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setFlowNodeMap(new HashMap<>());

        // 开始事件
        StartEventNodeCanvas startEvent = new StartEventNodeCanvas();
        startEvent.setId("start-1");
        startEvent.setName("开始");
        startEvent.setNextId("approval-1");

        // 审批节点
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setId("approval-1");
        approvalNode.setName("审批");
        approvalNode.setPrveId("start-1");
        approvalNode.setNextId("99");

        // 添加到流程映射
        processNode.getFlowNodeMap().put(startEvent.getId(), startEvent);
        processNode.getFlowNodeMap().put(approvalNode.getId(), approvalNode);
        processNode.setStartEventId(startEvent.getId());

        return processNode;
    }

    /**
     * 创建空的测试流程（用于EndOwnerManager基础测试）
     */
    private ProcessNode createEmptyTestProcess() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("testProcess");
        processNode.setStartEventId("start");
        processNode.setFlowNodeMap(new HashMap<>());

        // 添加开始节点
        StartEventNodeCanvas startNode = new StartEventNodeCanvas();
        startNode.setId("start");
        processNode.getFlowNodeMap().put("start", startNode);

        return processNode;
    }

    /**
     * 查找当前的EndOwner节点
     */
    private BaseNodeCanvas findEndOwner(ProcessNode processNode) {
        return processNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .filter(node -> "99".equals(((IRoutable) node).getNextId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 验证EndOwner唯一性和流程有效性
     */
    private void verifyWorkflowIntegrity(ProcessNode processNode, String scenario) {
        // 验证EndOwner唯一性
        long endOwnerCount = processNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();

        assertEquals(1, endOwnerCount, scenario + "应该只有一个EndOwner");

        // 验证流程结构有效性
        WorkflowValidator.ValidationResult result = workflowEditor.validate(processNode);
        assertTrue(result.isValid(), scenario + "流程结构应该有效: " + result.getErrors());
    }

    @Nested
    @DisplayName("EndOwner管理机制测试")
    class EndOwnerManagerTest {

        @Test
        @DisplayName("EndOwner基础功能测试")
        void testEndOwnerBasicOperations() {
            ProcessNode emptyProcess = createEmptyTestProcess();

            // 1. 测试没有EndOwner的情况
            assertFalse(endOwnerManager.validateEndOwnerUnique(emptyProcess));
            assertNull(endOwnerManager.findEndOwner(emptyProcess));
            assertEquals(0, endOwnerManager.countEndOwners(emptyProcess));
            assertTrue(endOwnerManager.findAllEndOwners(emptyProcess).isEmpty());

            // 2. 添加第一个EndOwner
            TestFlowNode endOwnerNode1 = new TestFlowNode();
            endOwnerNode1.setId("endOwner1");
            endOwnerNode1.setNextId("99");
            emptyProcess.getFlowNodeMap().put("endOwner1", endOwnerNode1);

            assertTrue(endOwnerManager.validateEndOwnerUnique(emptyProcess));
            assertEquals("endOwner1", endOwnerManager.findEndOwner(emptyProcess).getId());
            assertEquals(1, endOwnerManager.countEndOwners(emptyProcess));

            // 3. 添加第二个EndOwner（违反唯一性）
            TestFlowNode endOwnerNode2 = new TestFlowNode();
            endOwnerNode2.setId("endOwner2");
            endOwnerNode2.setNextId("99");
            emptyProcess.getFlowNodeMap().put("endOwner2", endOwnerNode2);

            assertFalse(endOwnerManager.validateEndOwnerUnique(emptyProcess));
            assertEquals(2, endOwnerManager.countEndOwners(emptyProcess));
            assertEquals(2, endOwnerManager.findAllEndOwners(emptyProcess).size());
        }

        @Test
        @DisplayName("EndOwner设置和扇入机制测试")
        void testEndOwnerSetAndFanIn() {
            ProcessNode emptyProcess = createEmptyTestProcess();

            // 1. 设置第一个EndOwner
            TestFlowNode node1 = new TestFlowNode();
            node1.setId("node1");
            emptyProcess.getFlowNodeMap().put("node1", node1);

            EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.setAsEndOwner(emptyProcess, node1);
            assertTrue(result.isSuccess());
            assertEquals("99", node1.getNextId());

            // 2. 设置第二个EndOwner（应该扇入到第一个）
            TestFlowNode node2 = new TestFlowNode();
            node2.setId("node2");
            emptyProcess.getFlowNodeMap().put("node2", node2);

            result = endOwnerManager.setAsEndOwner(emptyProcess, node2);
            assertTrue(result.isSuccess());
            assertEquals("node1", node2.getNextId()); // 扇入到第一个EndOwner

            // 验证仍然只有一个EndOwner
            assertTrue(endOwnerManager.validateEndOwnerUnique(emptyProcess));
        }

        @Test
        @DisplayName("连接节点到结束测试")
        void testConnectToEnd() {
            ProcessNode emptyProcess = createEmptyTestProcess();

            TestFlowNode node = new TestFlowNode();
            node.setId("node1");

            EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.connectToEnd(emptyProcess, node);
            assertTrue(result.isSuccess());
            assertEquals("99", node.getNextId());
        }

        /**
         * 测试用的FlowNode实现
         */
        private static class TestFlowNode extends FlowNodeCanvas {
            // 空实现，用于测试
        }
    }

    @Nested
    @DisplayName("网关操作测试")
    class GatewayOperationsTest {

        @Test
        @DisplayName("网关的创建、删除和类型切换")
        void testGatewayLifecycle() {
            // 1. 添加网关
            GatewayNodeCanvas gateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                                  GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

            assertNotNull(gateway);
            assertEquals(GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL, gateway.getGatewayType());
            assertEquals(2, gateway.getFlowIds().size());
            verifyWorkflowIntegrity(testProcessNode, "添加网关后");

            // 2. 切换网关类型
            workflowEditor.switchGatewayType(testProcessNode, gateway.getId(),
                                           GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);

            gateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());
            assertEquals(GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE, gateway.getGatewayType());
            verifyWorkflowIntegrity(testProcessNode, "切换网关类型后");

            // 3. 删除分支直到触发网关删除
            String firstBranchId = gateway.getFlowIds().get(0);
            workflowEditor.deleteBranch(testProcessNode, gateway.getId(), firstBranchId);

            // 网关应被自动删除（扁平化）
            assertNull(testProcessNode.getFlowNodeMap().get(gateway.getId()));
            verifyWorkflowIntegrity(testProcessNode, "网关扁平化后");
        }

        @Test
        @DisplayName("网关EndOwner管理策略")
        void testGatewayEndOwnerManagement() {
            // 创建网关链：A -> B -> C
            GatewayNodeCanvas gatewayA = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                                   GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

            GatewayNodeCanvas gatewayB = workflowEditor.addGateway(testProcessNode, gatewayA.getId(),
                                                                   GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

            GatewayNodeCanvas gatewayC = workflowEditor.addGateway(testProcessNode, gatewayB.getId(),
                                                                   GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

            // 验证EndOwner是最后的网关C
            BaseNodeCanvas endOwner = findEndOwner(testProcessNode);
            assertEquals(gatewayC.getId(), endOwner.getId(), "EndOwner应该是最后的网关C");

            // 在C的分支中插入网关，EndOwner仍应是C
            String cBranchId = gatewayC.getFlowIds().get(0);
            workflowEditor.addGateway(testProcessNode, cBranchId, GatewayOperations.PlacementStrategy.NO_MOVE);

            endOwner = findEndOwner(testProcessNode);
            assertEquals(gatewayC.getId(), endOwner.getId(), "在分支中插入网关后，EndOwner应该还是C");

            // 在C前插入网关D，EndOwner应转移到D
            GatewayNodeCanvas gatewayD = workflowEditor.addGateway(testProcessNode, gatewayB.getId(),
                                                                   GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

            endOwner = findEndOwner(testProcessNode);
            assertEquals(gatewayD.getId(), endOwner.getId(), "在主链前插入网关后，EndOwner应该转移到D");

            verifyWorkflowIntegrity(testProcessNode, "复杂网关嵌套后");
        }
    }

    @Nested
    @DisplayName("分支操作测试")
    class BranchOperationsTest {

        @Test
        @DisplayName("分支的增删改操作")
        void testBranchOperations() {
            // 1. 添加网关并切换为唯一分支
            GatewayNodeCanvas gateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                                  GatewayOperations.PlacementStrategy.NO_MOVE);
            workflowEditor.switchGatewayType(testProcessNode, gateway.getId(),
                                           GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);

            // 2. 添加分支
            workflowEditor.addBranch(testProcessNode, gateway.getId(), -1);

            gateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());
            assertEquals(3, gateway.getFlowIds().size());

            // 3. 复制分支
            String originalBranchId = gateway.getFlowIds().get(0);
            ConditionNodeCanvas duplicatedBranch = workflowEditor.duplicateBranch(testProcessNode,
                                                                                  gateway.getId(),
                                                                                  originalBranchId, -1);

            assertNotNull(duplicatedBranch);
            gateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());
            assertEquals(4, gateway.getFlowIds().size());

            // 4. 调整分支顺序
            List<String> newOrder = new ArrayList<>(gateway.getFlowIds());
            Collections.reverse(newOrder);
            workflowEditor.reorderBranches(testProcessNode, gateway.getId(), newOrder);

            verifyWorkflowIntegrity(testProcessNode, "分支操作后");
        }
    }

    @Nested
    @DisplayName("节点操作测试")
    class NodeOperationsTest {

        @Test
        @DisplayName("节点的增删改操作")
        void testNodeOperations() {
            // 1. 插入节点
            WriteNode writeNode = new WriteNode();
            writeNode.setId("write-test");
            writeNode.setName("原始名称");
            BaseNodeCanvas insertedNode = workflowEditor.insertNode(testProcessNode, "start-1", writeNode);

            assertNotNull(insertedNode);
            assertEquals("原始名称", insertedNode.getName());
            verifyWorkflowIntegrity(testProcessNode, "插入节点后");

            // 2. 更新节点
            Map<String, Object> updates = new HashMap<>();
            updates.put("name", "更新后的名称");
            workflowEditor.updateNode(testProcessNode, "write-test", updates);

            BaseNodeCanvas updatedNode = testProcessNode.getFlowNodeMap().get("write-test");
            assertEquals("更新后的名称", updatedNode.getName());

            // 3. 删除EndOwner节点，验证自动重选EndOwner
            workflowEditor.deleteNode(testProcessNode, "approval-1");

            BaseNodeCanvas newEndOwner = findEndOwner(testProcessNode);
            assertNotNull(newEndOwner, "删除原EndOwner后应该重新选择EndOwner");
            // 验证新的EndOwner是合理的（应该是剩余节点中的一个）
            assertTrue(testProcessNode.getFlowNodeMap().containsKey(newEndOwner.getId()),
                      "新EndOwner应该存在于流程中");

            verifyWorkflowIntegrity(testProcessNode, "删除节点后");
        }

        @Test
        @DisplayName("子流程节点操作")
        void testSubProcessOperations() {
            // 创建子流程节点
            ApprovalProcessNode subProcessNode = new ApprovalProcessNode();
            subProcessNode.setId("subprocess-new");
            subProcessNode.setName("审批子流程");
            workflowEditor.insertNode(testProcessNode, "start-1", subProcessNode);

            // 创建子流程
            ProcessNode subProcess = createBasicWorkflow();
            subProcess.setId("sub-process");
            subProcessNode.setProcessNode(subProcess);

            // 在子流程中添加节点
            ApprovalNode approvalNode = new ApprovalNode();
            approvalNode.setId("sub-approval-new");
            approvalNode.setName("子流程审批");
            workflowEditor.insertNode(subProcess, "start-1", approvalNode);

            // 验证主流程和子流程都只有一个EndOwner
            verifyWorkflowIntegrity(testProcessNode, "主流程");
            verifyWorkflowIntegrity(subProcess, "子流程");
        }
    }

    @Test
    @DisplayName("复合场景：混合操作集成测试")
    void testComplexMixedOperations() {
        // 创建一个包含多种操作的复杂场景

        // 1. 添加主网关
        GatewayNodeCanvas mainGateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                                  GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

        // 2. 在分支中添加节点和子网关
        String branch1 = mainGateway.getFlowIds().get(0);
        WriteNode writeNode = new WriteNode();
        writeNode.setId("write-form");
        writeNode.setName("填写表单");
        workflowEditor.insertNode(testProcessNode, branch1, writeNode);

        GatewayNodeCanvas subGateway = workflowEditor.addGateway(testProcessNode, "write-form",
                                                                 GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

        // 3. 切换网关类型并操作分支
        workflowEditor.switchGatewayType(testProcessNode, subGateway.getId(),
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);

        String originalBranch = subGateway.getFlowIds().get(0);
        workflowEditor.duplicateBranch(testProcessNode, subGateway.getId(), originalBranch, -1);

        // 4. 在主网关后添加合流处理
        WriteNode postNode = new WriteNode();
        postNode.setId("post-process");
        postNode.setName("后处理");
        workflowEditor.insertNode(testProcessNode, mainGateway.getId(), postNode);

        // 验证最终结构
        BaseNodeCanvas endOwner = findEndOwner(testProcessNode);
        assertEquals("post-process", endOwner.getId(), "最终EndOwner应该是后处理节点");

        verifyWorkflowIntegrity(testProcessNode, "复合操作后");

        // 验证节点数量合理性
        long gatewayCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof GatewayNodeCanvas)
                .count();

        long nodeCount = testProcessNode.getFlowNodeMap().size();

        assertTrue(gatewayCount >= 2, "应该有至少2个网关");
        assertTrue(nodeCount >= 8, "应该有至少8个节点");

        log.info("复合操作测试完成 - 网关数量: {}, 总节点数: {}, EndOwner: {}",
                gatewayCount, nodeCount, endOwner.getId());
    }

    @Test
    @DisplayName("边界场景：极端操作测试")
    void testEdgeCases() {
        // 测试各种边界场景

        // 1. 连续添加和删除网关
        GatewayNodeCanvas gateway1 = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                               GatewayOperations.PlacementStrategy.NO_MOVE);

        GatewayNodeCanvas gateway2 = workflowEditor.addGateway(testProcessNode, gateway1.getId(),
                                                               GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

        // 删除第一个网关的分支直到扁平化
        String branchToDelete = gateway1.getFlowIds().get(0);
        workflowEditor.deleteBranch(testProcessNode, gateway1.getId(), branchToDelete);

        // 验证gateway1被删除，gateway2成为EndOwner
        assertNull(testProcessNode.getFlowNodeMap().get(gateway1.getId()));
        assertNotNull(testProcessNode.getFlowNodeMap().get(gateway2.getId()));

        BaseNodeCanvas endOwner = findEndOwner(testProcessNode);
        assertEquals(gateway2.getId(), endOwner.getId());

        // 2. 在网关分支中添加多层嵌套
        String g2Branch = gateway2.getFlowIds().get(0);

        WriteNode nestedWrite = new WriteNode();
        nestedWrite.setId("nested-write");
        nestedWrite.setName("嵌套写入");
        workflowEditor.insertNode(testProcessNode, g2Branch, nestedWrite);

        GatewayNodeCanvas nestedGateway = workflowEditor.addGateway(testProcessNode, "nested-write",
                                                                    GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

        // 验证EndOwner转移
        endOwner = findEndOwner(testProcessNode);
        assertEquals(nestedGateway.getId(), endOwner.getId());

        verifyWorkflowIntegrity(testProcessNode, "边界场景操作后");
    }
}
