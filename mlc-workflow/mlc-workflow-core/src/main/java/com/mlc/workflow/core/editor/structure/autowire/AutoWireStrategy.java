package com.mlc.workflow.core.editor.structure.autowire;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.utils.ValidationUtils;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 通用自动连线策略
 * 实现设计方案中的核心连线原语：Detach、SpliceBetween、Replace、ConnectToEnd等
 */
@Slf4j
public class AutoWireStrategy {
    
    private final EndOwnerManager endOwnerManager;
    
    public AutoWireStrategy(EndOwnerManager endOwnerManager) {
        this.endOwnerManager = endOwnerManager;
    }
    
    /**
     * 断开操作：将指定的链段从流程中断开
     * @param processNode 流程节点
     * @param head 链段头节点
     * @param tail 链段尾节点
     * @return 断开操作的上下文信息
     */
    public DetachContext detach(ProcessNode processNode, BaseNodeCanvas head, BaseNodeCanvas tail) {
        // 使用统一的参数校验
        ValidationUtils.validateAutoWireParams(processNode, head, tail);

        DetachContext context = new DetachContext();

        // 找到所有指向head的前驱节点
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, head.getId());
        context.setPrevNodes(prevNodes);

        // 保存tail的下一个节点
        if (tail instanceof IRoutable routableTail) {
            context.setOriginalNext(routableTail.getNextId());
        }

        // 断开前驱节点的连接
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(null); // 暂置为null
            }
        }

        // 断开tail的连接
        if (tail instanceof IRoutable routableTail) {
            routableTail.setNextId(null);
        }

        log.debug("断开链段 {} -> {}, 前驱节点数: {}", head.getId(), tail.getId(), prevNodes.size());

        return context;
    }
    
    /**
     * 拼接操作：将链段拼接到指定位置
     * @param processNode 流程节点
     * @param prevNodes 前驱节点列表
     * @param head 链段头节点
     * @param tail 链段尾节点
     * @param nextNodeId 下一个节点ID
     */
    public void spliceBetween(ProcessNode processNode, List<BaseNodeCanvas> prevNodes,
                            BaseNodeCanvas head, BaseNodeCanvas tail, String nextNodeId) {
        // 使用统一的参数校验
        ValidationUtils.validateAutoWireParams(processNode, head, tail);

        // 连接前驱节点到head
        if (prevNodes != null && !prevNodes.isEmpty()) {
            for (BaseNodeCanvas prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(head.getId());
                }
            }

            // 设置head的prveId指向第一个前驱节点（如果head是可路由的）
            if (head instanceof IRoutable routableHead && !prevNodes.isEmpty()) {
                routableHead.setPrveId(prevNodes.get(0).getId());
            }
        }

        // 连接tail到下一个节点
        if (tail instanceof IRoutable routableTail) {
            routableTail.setNextId(nextNodeId);
        }

        log.debug("拼接链段 {} -> {} 到位置，前驱节点数: {}, 下一个节点: {}",
                head.getId(), tail.getId(), prevNodes != null ? prevNodes.size() : 0,
                ValidationUtils.safeGetNodeId(processNode.getFlowNodeMap().get(nextNodeId)));
    }
    
    /**
     * 替换操作：用新链段替换旧链段
     * @param processNode 流程节点
     * @param oldHead 旧链段头节点
     * @param oldTail 旧链段尾节点
     * @param newHead 新链段头节点
     * @param newTail 新链段尾节点
     */
    public void replace(ProcessNode processNode, BaseNodeCanvas oldHead, BaseNodeCanvas oldTail,
                       BaseNodeCanvas newHead, BaseNodeCanvas newTail) {
        if (processNode == null || oldHead == null || oldTail == null || 
            newHead == null || newTail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        // 断开旧链段
        DetachContext detachContext = detach(processNode, oldHead, oldTail);
        
        // 拼接新链段
        spliceBetween(processNode, detachContext.getPrevNodes(), newHead, newTail, 
                     detachContext.getOriginalNext());
        
        log.debug("替换链段 {} -> {} 为 {} -> {}", 
                oldHead.getId(), oldTail.getId(), newHead.getId(), newTail.getId());
    }
    
    /**
     * 连接到结束：将节点连接到流程结束
     * 实现设计方案中的ConnectToEnd原语，严格遵循EndOwner不变量
     * @param processNode 流程节点
     * @param tail 尾节点
     */
    public void connectToEnd(ProcessNode processNode, BaseNodeCanvas tail) {
        if (processNode == null || tail == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.connectToEnd(processNode, tail);
        if (!result.isSuccess()) {
            String errorMsg = "连接到结束失败: " + String.join(", ", result.getErrors());
            log.error(errorMsg);
            throw new RuntimeException(errorMsg);
        }

        log.debug("成功连接节点 {} 到流程结束，EndOwner: {}",
                 tail.getId(), result.getNewEndOwner().getId());
    }

    /**
     * 修复EndOwner：当结构扁平化导致EndOwner不再是实际末尾时
     * 实现设计方案中的AbortEndOwnerIfFlatten原语
     * @param processNode 流程节点
     */
    public void abortEndOwnerIfFlatten(ProcessNode processNode) {
        if (processNode == null) {
            throw new IllegalArgumentException("ProcessNode不能为空");
        }

        EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.repairEndOwnerAfterFlatten(processNode);

        if (!result.isSuccess()) {
            if (!result.getErrors().isEmpty()) {
                String errorMsg = "修复EndOwner失败: " + String.join(", ", result.getErrors());
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            } else if (!result.getWarnings().isEmpty()) {
                log.warn("修复EndOwner有警告: {}", String.join(", ", result.getWarnings()));
            }
        } else {
            log.debug("EndOwner修复成功，当前EndOwner: {}",
                     result.getNewEndOwner() != null ? result.getNewEndOwner().getId() : "无");
        }
    }
    
    /**
     * 断开操作的上下文信息
     */
    @Setter
    @Getter
    public static class DetachContext {
        private List<BaseNodeCanvas> prevNodes;
        private String originalNext;
    }
}
