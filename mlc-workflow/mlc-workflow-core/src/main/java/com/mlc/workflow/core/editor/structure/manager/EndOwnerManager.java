package com.mlc.workflow.core.editor.structure.manager;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * EndOwner 管理器
 * 负责维护流程中 EndOwner 的唯一性和一致性，可能会因为结构扁平化等操作需要重新选择
 * EndOwner 是流程的结束点，nextId="99"，每个流程只能有一个
 * EndOwner 是一种机制，允许多个节点扇入到同一个结束点
 */
@Slf4j
public class EndOwnerManager {

    /**
     * EndOwner 标识
     */
    public static final String END_OWNER_ID = "99";

    // 分支结束符号
    public static final String BRANCH_END_ID = "";
    
    /**
     * 查找当前的 EndOwner
     * @param processNode 流程节点
     * @return EndOwner 节点，如果没有则返回 null
     */
    public BaseNodeCanvas findEndOwner(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return null;
        }
        
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    return node;
                }
            }
        }
        
        return null;
    }
    
    /**
     * 验证 EndOwner 的唯一性
     * @param processNode 流程节点
     * @return 验证结果，true 表示唯一性满足（恰好有一个EndOwner），false 表示不满足
     */
    public boolean validateEndOwnerUnique(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            throw new IllegalArgumentException("ProcessNode 不能为空");
        }

        int endOwnerCount = 0;
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    endOwnerCount++;
                }
            }
        }

        // 根据设计方案：同一ProcessNode内，恰有1个节点的nextId=99
        return endOwnerCount == 1;
    }

    /**
     * 统计 EndOwner 数量
     * @param processNode 流程节点
     * @return EndOwner 数量
     */
    public int countEndOwners(ProcessNode processNode) {
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return 0;
        }

        int count = 0;
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 查找所有 EndOwner 节点
     * @param processNode 流程节点
     * @return EndOwner 节点列表
     */
    public List<BaseNodeCanvas> findAllEndOwners(ProcessNode processNode) {
        List<BaseNodeCanvas> endOwners = new ArrayList<>();
        if (processNode == null || processNode.getFlowNodeMap() == null) {
            return endOwners;
        }

        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                if (END_OWNER_ID.equals(routableNode.getNextId())) {
                    endOwners.add(node);
                }
            }
        }
        return endOwners;
    }


    /**
     * 设置节点为 EndOwner
     * 严格按照设计方案：任何新增"指向结束"的请求，必须转化为"接到EndOwner之前"
     * @param processNode 流程节点
     * @param node 要设置为 EndOwner 的节点
     * @return 操作结果
     */
    public EndOwnerOperationResult setAsEndOwner(ProcessNode processNode, BaseNodeCanvas node) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();

        if (!(node instanceof IRoutable routableNode)) {
            result.addError("节点不是可路由的，无法设置为 EndOwner");
            return result;
        }

        // 检查当前是否已有 EndOwner
        BaseNodeCanvas currentEndOwner = findEndOwner(processNode);

        if (currentEndOwner != null) {
            if (currentEndOwner.getId().equals(node.getId())) {
                // 节点已经是 EndOwner，无需操作
                result.setSuccess(true);
                result.setNewEndOwner(currentEndOwner);
                log.debug("节点 {} 已经是 EndOwner", node.getId());
            } else {
                // 已有其他 EndOwner，根据设计方案，必须扇入到现有 EndOwner
                result = handleFanInToEndOwner(node, currentEndOwner);
                log.debug("节点 {} 扇入到现有 EndOwner {}", node.getId(), currentEndOwner.getId());
            }
        } else {
            // 没有 EndOwner，直接设置为 EndOwner
            routableNode.setNextId(END_OWNER_ID);
            result.setSuccess(true);
            result.setNewEndOwner(node);
            log.debug("设置节点 {} 为流程的第一个 EndOwner", node.getId());
        }

        return result;
    }
    
    /**
     * 处理扇入到 EndOwner
     * 根据设计方案：保证仍只有一个节点的nextId=99，其他节点扇入到EndOwner
     * @param newNode 新节点
     * @param currentEndOwner 当前 EndOwner
     * @return 操作结果
     */
    private EndOwnerOperationResult handleFanInToEndOwner(BaseNodeCanvas newNode, BaseNodeCanvas currentEndOwner) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();

        if (!(newNode instanceof IRoutable routableNew)) {
            result.addError("新节点不是可路由的");
            return result;
        }

        if (!(currentEndOwner instanceof IRoutable routableEndOwner)) {
            result.addError("当前EndOwner不是可路由的");
            return result;
        }

        // 验证当前EndOwner确实指向99
        if (!END_OWNER_ID.equals(routableEndOwner.getNextId())) {
            result.addError("当前EndOwner的nextId不是99，数据不一致");
            return result;
        }

        // 新节点扇入到现有 EndOwner（形成扇入结构）
        routableNew.setNextId(currentEndOwner.getId());
        result.setSuccess(true);
        result.setNewEndOwner(currentEndOwner); // EndOwner保持不变
        result.setFanInNode(newNode);

        log.debug("节点 {} 扇入到 EndOwner {}，保持EndOwner唯一性", newNode.getId(), currentEndOwner.getId());

        return result;
    }
    
    /**
     * 连接节点到结束
     * 严格按照设计方案的ConnectToEnd原语：
     * - 若当前流程无EndOwner：设置tail.nextId=99，登记为EndOwner
     * - 若已有EndOwner：禁止再设99，设置tail.nextId=E.id，形成扇入
     * @param processNode 流程节点
     * @param baseNodeCanvas 要连接的节点
     * @return 操作结果
     */
    public EndOwnerOperationResult connectToEnd(ProcessNode processNode, BaseNodeCanvas baseNodeCanvas) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();

        if (!(baseNodeCanvas instanceof IRoutable routableNode)) {
            result.addError("节点不是可路由的，无法连接到结束");
            return result;
        }

        BaseNodeCanvas currentEndOwner = findEndOwner(processNode);

        if (currentEndOwner == null) {
            // 若当前流程无EndOwner：设置tail.nextId=99，登记为EndOwner
            routableNode.setNextId(END_OWNER_ID);
            result.setSuccess(true);
            result.setNewEndOwner(baseNodeCanvas);
            log.debug("流程无EndOwner，设置节点 {} 为EndOwner", baseNodeCanvas.getId());
        } else {
            // 若已有EndOwner：禁止再设99，设置tail.nextId=E.id，形成扇入
            result = handleFanInToEndOwner(baseNodeCanvas, currentEndOwner);
            log.debug("流程已有EndOwner {}，节点 {} 扇入", currentEndOwner.getId(), baseNodeCanvas.getId());
        }

        return result;
    }
    
    /**
     * 修复 EndOwner：当结构扁平化导致 EndOwner 不再是实际末尾时
     * 实现设计方案中的AbortEndOwnerIfFlatten原语
     * @param processNode 流程节点
     * @return 修复结果
     */
    public EndOwnerOperationResult repairEndOwnerAfterFlatten(ProcessNode processNode) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();

        // 检查EndOwner唯一性
        List<BaseNodeCanvas> allEndOwners = findAllEndOwners(processNode);
        if (allEndOwners.isEmpty()) {
            // 没有EndOwner，尝试自动修复：找到一个合适的节点设为EndOwner
            return autoCreateEndOwner(processNode, result);
        }

        if (allEndOwners.size() > 1) {
            result.addError("发现多个EndOwner，违反唯一性约束: " +
                          allEndOwners.stream().map(BaseNodeCanvas::getId).toList());
            return result;
        }

        BaseNodeCanvas currentEndOwner = allEndOwners.get(0);

        // 检查是否有其他节点指向 EndOwner
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, currentEndOwner.getId());

        if (prevNodes.isEmpty()) {
            // EndOwner 没有前驱，检查是否是开始节点
            if (currentEndOwner.getId().equals(processNode.getStartEventId())) {
                result.setSuccess(true);
                result.setNewEndOwner(currentEndOwner);
                log.debug("EndOwner {} 是开始节点，无需修复", currentEndOwner.getId());
            } else {
                result.addWarning("EndOwner {} 没有前驱节点且不是开始节点，可能是孤立节点");
            }
            return result;
        }

        // 如果有多个前驱指向 EndOwner，需要重新组织结构
        if (prevNodes.size() > 1) {
            return reorganizeEndOwnerStructure(processNode, currentEndOwner, prevNodes, result);
        } else {
            // 只有一个前驱，结构正常
            result.setSuccess(true);
            result.setNewEndOwner(currentEndOwner);
            log.debug("EndOwner {} 结构正常，只有一个前驱 {}",
                     currentEndOwner.getId(), prevNodes.get(0).getId());
        }

        return result;
    }

    /**
     * 自动创建EndOwner
     * 当流程中没有EndOwner时，选择一个合适的节点设为EndOwner
     * @param processNode 流程节点
     * @param result 操作结果
     * @return 操作结果
     */
    private EndOwnerOperationResult autoCreateEndOwner(ProcessNode processNode, EndOwnerOperationResult result) {
        if (processNode == null || processNode.getFlowNodeMap() == null || processNode.getFlowNodeMap().isEmpty()) {
            result.addError("流程为空，无法创建EndOwner");
            return result;
        }

        // 查找所有没有后继的节点（可能的EndOwner候选）
        List<BaseNodeCanvas> candidateNodes = new ArrayList<>();
        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                String nextId = routableNode.getNextId();
                if (nextId == null || nextId.trim().isEmpty() || BRANCH_END_ID.equals(nextId)) {
                    candidateNodes.add(node);
                }
            }
        }

        if (candidateNodes.isEmpty()) {
            result.addError("没有找到合适的EndOwner候选节点");
            return result;
        }

        // 选择最合适的节点作为EndOwner
        BaseNodeCanvas newEndOwner = selectNewEndOwner(candidateNodes);
        if (!(newEndOwner instanceof IRoutable routableNewEnd)) {
            result.addError("选择的EndOwner候选节点不是可路由的");
            return result;
        }

        // 设置为EndOwner
        routableNewEnd.setNextId(END_OWNER_ID);
        result.setSuccess(true);
        result.setNewEndOwner(newEndOwner);

        log.debug("自动创建EndOwner: {}，从 {} 个候选节点中选择", newEndOwner.getId(), candidateNodes.size());
        return result;
    }

    /**
     * 重新组织EndOwner结构
     * 当有多个前驱指向EndOwner时，选择一个作为新的EndOwner，其他扇入
     * @param processNode 流程节点
     * @param currentEndOwner 当前EndOwner
     * @param prevNodes 前驱节点列表
     * @param result 操作结果
     * @return 操作结果
     */
    private EndOwnerOperationResult reorganizeEndOwnerStructure(ProcessNode processNode,
                                                              BaseNodeCanvas currentEndOwner,
                                                              List<BaseNodeCanvas> prevNodes,
                                                              EndOwnerOperationResult result) {
        // 选择新的EndOwner
        BaseNodeCanvas newEndOwner = selectNewEndOwner(prevNodes);
        if (!(newEndOwner instanceof IRoutable routableNewEnd)) {
            result.addError("选择的新EndOwner不是可路由的");
            return result;
        }

        // 设置新的EndOwner
        routableNewEnd.setNextId(END_OWNER_ID);

        // 其他前驱指向新的 EndOwner（形成扇入）
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (!prevNode.getId().equals(newEndOwner.getId()) &&
                prevNode instanceof IRoutable routablePrev) {

                // 检查前驱节点是否是网关的分支叶子
                if (isGatewayBranchLeaf(processNode, prevNode)) {
                    // 网关的分支叶子不应该指向其他节点，保持为空字符串
                    log.debug("跳过网关分支叶子 {} 的nextId修改", prevNode.getId());
                    continue;
                }

                routablePrev.setNextId(newEndOwner.getId());
            }
        }

        // 原 EndOwner 不再是结束节点
        if (currentEndOwner instanceof IRoutable routableOldEnd) {
            routableOldEnd.setNextId("");
        }

        result.setSuccess(true);
        result.setNewEndOwner(newEndOwner);
        result.setOldEndOwner(currentEndOwner);

        log.debug("重新组织EndOwner结构: {} -> {}，{} 个前驱扇入",
                 currentEndOwner.getId(), newEndOwner.getId(), prevNodes.size() - 1);

        return result;
    }

    /**
     * 检查节点是否是网关的分支叶子
     * @param processNode 流程节点
     * @param node 要检查的节点
     * @return 是否是网关的分支叶子
     */
    private boolean isGatewayBranchLeaf(ProcessNode processNode, BaseNodeCanvas node) {
        if (!(node instanceof IRoutable routableNode)) {
            return false;
        }

        String prveId = routableNode.getPrveId();
        if (prveId == null || prveId.trim().isEmpty()) {
            return false;
        }

        BaseNodeCanvas prevNode = processNode.getFlowNodeMap().get(prveId);
        if (!(prevNode instanceof GatewayNodeCanvas gateway)) {
            return false;
        }

        // 检查该节点是否在网关的分支列表中
        List<String> flowIds = gateway.getFlowIds();
        return flowIds != null && flowIds.contains(node.getId());
    }

    /**
     * 选择新的 EndOwner
     * @param candidates 候选节点列表
     * @return 选择的新 EndOwner
     */
    private BaseNodeCanvas selectNewEndOwner(List<BaseNodeCanvas> candidates) {
        if (candidates == null || candidates.isEmpty()) {
            throw new IllegalArgumentException("候选节点列表不能为空");
        }

        // 策略：选择最后一个候选节点作为新的EndOwner
        // 这样可以保持流程的逻辑顺序
        return candidates.get(candidates.size() - 1);
    }
    
    /**
     * 删除 EndOwner 时的处理
     * 确保删除后仍然保持EndOwner唯一性约束
     * @param processNode 流程节点
     * @param endOwnerToDelete 要删除的 EndOwner
     * @return 操作结果
     */
    public EndOwnerOperationResult handleEndOwnerDeletion(ProcessNode processNode, BaseNodeCanvas endOwnerToDelete) {
        EndOwnerOperationResult result = new EndOwnerOperationResult();

        // 验证要删除的节点确实是EndOwner
        if (!(endOwnerToDelete instanceof IRoutable routableEndOwner) ||
            !END_OWNER_ID.equals(routableEndOwner.getNextId())) {
            result.addError("要删除的节点不是EndOwner");
            return result;
        }

        // 查找前驱节点
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, endOwnerToDelete.getId());

        if (prevNodes.isEmpty()) {
            // 没有前驱节点，检查是否是唯一节点
            if (processNode.getFlowNodeMap().size() <= 1) {
                result.addWarning("删除的EndOwner是流程中的唯一节点");
                result.setSuccess(true);
                return result;
            } else {
                result.addError("删除的EndOwner没有前驱节点，但流程中还有其他节点，结构异常");
                return result;
            }
        }

        // 选择新的 EndOwner
        BaseNodeCanvas newEndOwner = selectNewEndOwner(prevNodes);
        if (!(newEndOwner instanceof IRoutable routableNewEnd)) {
            result.addError("选择的新EndOwner不是可路由的");
            return result;
        }

        // 设置新的EndOwner
        routableNewEnd.setNextId(END_OWNER_ID);

        // 其他前驱指向新的 EndOwner（形成扇入）
        for (BaseNodeCanvas prevNode : prevNodes) {
            if (!prevNode.getId().equals(newEndOwner.getId()) && prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(newEndOwner.getId());
            }
        }

        result.setSuccess(true);
        result.setNewEndOwner(newEndOwner);
        result.setOldEndOwner(endOwnerToDelete);

        log.debug("删除 EndOwner {} 后重新选择 EndOwner: {}，{} 个前驱扇入",
                 endOwnerToDelete.getId(), newEndOwner.getId(), prevNodes.size() - 1);

        return result;
    }

    
    /**
     * EndOwner 操作结果
     */
    @Getter
    public static class EndOwnerOperationResult {
        @Setter
        private boolean success = false;
        @Setter
        private BaseNodeCanvas newEndOwner;
        @Setter
        private BaseNodeCanvas oldEndOwner;
        @Setter
        private BaseNodeCanvas fanInNode;
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();

        public void addError(String error) { this.errors.add(error); }

        public void addWarning(String warning) { this.warnings.add(warning); }
    }
}
