package com.mlc.workflow.core.editor.structure.operation;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.ValidationUtils;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

/**
 * 网关操作命令
 * 实现网关的新增、删除、类型切换等操作
 * 优化后使用AutoWireStrategy原语，遵循设计方案
 */
@Slf4j
public class GatewayOperations {

    private final AutoWireStrategy autoWireStrategy;
    private final NodeBatchExecutor nodeBatchExecutor;

    public GatewayOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.autoWireStrategy = autoWireStrategy;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }

    /**
     * 放置策略枚举
     */
    public enum PlacementStrategy {
        LEFT_PLACEMENT,  // 左侧放置（移动原有链段到左分支）
        NO_MOVE         // 不移动（在原位置插入网关）
    }

    /**
     * 新增网关（默认并行）
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement) {
        return addGateway(atNodeId, placement, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
    }

    /**
     * 新增网关
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @param gatewayType 网关类型
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement, Integer gatewayType) {
        // 使用统一的参数校验
        ValidationUtils.validateGatewayOperationParams(atNodeId, "atNodeId");
        ValidationUtils.validateExecutorState(nodeBatchExecutor);
        ValidationUtils.validateGatewayType(gatewayType);

        // 从工作副本获取节点
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas atNode = ValidationUtils.requireNodeExists(workingCopy, atNodeId, "插入点");
        IRoutable routableAtNode = ValidationUtils.requireRoutable(atNode, "插入点节点");

        String originalNextId = routableAtNode.getNextId();

        // 创建网关和分支叶子
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setName("网关");
        gateway.setGatewayType(gatewayType); // 设置网关类型

        ConditionNodeCanvas leftBranch = new ConditionNodeCanvas();
        leftBranch.setName("分支1");

        ConditionNodeCanvas rightBranch = new ConditionNodeCanvas();
        rightBranch.setName("分支2");

        // 如果是唯一分支网关，为分支叶子设置默认条件
        if (gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            setDefaultConditionForBranch(leftBranch, 0);
            setDefaultConditionForBranch(rightBranch, 1);
        }

        // 设置网关的分支
        gateway.getFlowIds().add(leftBranch.getId());
        gateway.getFlowIds().add(rightBranch.getId());

        // 设置分支叶子的前驱
        leftBranch.setPrveId(gateway.getId());
        rightBranch.setPrveId(gateway.getId());

        nodeBatchExecutor.createNode(gateway);
        nodeBatchExecutor.createNode(leftBranch);
        nodeBatchExecutor.createNode(rightBranch);

        // 根据放置策略处理
        if (placement == PlacementStrategy.LEFT_PLACEMENT) {
            handleLeftPlacement(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        } else {
            handleNoMove(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        }

        log.debug("在节点 {} 后新增网关 {}，策略: {}", atNodeId, gateway.getId(), placement);

        return gateway;
    }

    /**
     * 处理左侧放置策略
     * 优化后使用AutoWireStrategy原语，遵循设计方案
     */
    private void handleLeftPlacement(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {

        // 直接连接atNode到网关（不使用spliceBetween，因为这会导致循环）
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());

        // 记录节点变更
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);

        if (originalNextId != null && !originalNextId.trim().isEmpty()) {
            if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
                // 原来指向结束，根据设计方案：网关层级保持EndOwner
                autoWireStrategy.connectToEnd(processNode, gateway);
                log.debug("左侧放置：原节点指向结束，网关 {} 成为新的EndOwner载体", gateway.getId());
            } else {
                // 将原有的下游链段移动到左分支
                // 使用WorkflowQueryService.findBranchChain来查找从原下游开始的链段
                List<BaseNodeCanvas> originalChain = WorkflowQueryService.findBranchChain(processNode, originalNextId);

                // 检查原下游节点是否直接指向EndOwner
                BaseNodeCanvas originalNext = processNode.getFlowNodeMap().get(originalNextId);
                boolean originalNextIsEndOwner = originalNext instanceof IRoutable routableNext &&
                    EndOwnerManager.END_OWNER_ID.equals(routableNext.getNextId());

                if (originalNextIsEndOwner) {
                    // 原下游直接指向EndOwner，需要转移EndOwner到网关
                    ((IRoutable) originalNext).setNextId(""); // 断开原EndOwner连接
                    nodeBatchExecutor.updateNode(originalNext.getId(), originalNext);

                    // 连接左分支到原下游
                    leftBranch.setNextId(originalNextId);
                    ((IRoutable) originalNext).setPrveId(leftBranch.getId());
                    nodeBatchExecutor.updateNode(originalNext.getId(), originalNext);

                    // 网关成为新的EndOwner
                    autoWireStrategy.connectToEnd(processNode, gateway);
                    log.debug("左侧放置：转移EndOwner到新网关 {}", gateway.getId());
                } else {
                    // 原下游不直接指向EndOwner，直接移动
                    leftBranch.setNextId(originalNextId);
                    if (originalNext instanceof IRoutable routableNext) {
                        routableNext.setPrveId(leftBranch.getId());
                        nodeBatchExecutor.updateNode(originalNext.getId(), originalNext);
                    }

                    // 检查链段中是否有EndOwner
                    boolean chainHasEndOwner = originalChain.stream()
                        .filter(node -> node instanceof IRoutable)
                        .anyMatch(node -> EndOwnerManager.END_OWNER_ID.equals(((IRoutable) node).getNextId()));

                    if (chainHasEndOwner) {
                        // 链段中有EndOwner，需要转移到网关
                        originalChain.stream()
                            .filter(node -> node instanceof IRoutable)
                            .filter(node -> EndOwnerManager.END_OWNER_ID.equals(((IRoutable) node).getNextId()))
                            .forEach(node -> {
                                ((IRoutable) node).setNextId("");
                                nodeBatchExecutor.updateNode(node.getId(), node);
                            });

                        autoWireStrategy.connectToEnd(processNode, gateway);
                        log.debug("左侧放置：从链段转移EndOwner到新网关 {}", gateway.getId());
                    } else {
                        // 链段中没有EndOwner，网关不需要特殊处理
                        gateway.setNextId(""); // 网关暂时不指向任何地方
                        log.debug("左侧放置：移动原链段到左分支，网关暂不连接", gateway.getId());
                    }
                }
            }
        }

        // 右分支设为空分支
        rightBranch.setNextId("");
    }

    /**
     * 处理不移动策略
     * 优化后使用AutoWireStrategy原语，遵循设计方案
     */
    private void handleNoMove(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
        ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {

        // 直接连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());

        // 记录节点变更
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);

        // 处理网关的下游连接
        if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
            // 原来指向结束，使用AutoWireStrategy.connectToEnd
            autoWireStrategy.connectToEnd(processNode, gateway);
            log.debug("不移动策略：网关 {} 连接到结束", gateway.getId());
        } else if (originalNextId != null && !originalNextId.trim().isEmpty()) {
            // 网关合流后继续原有下游，更新下游节点的前驱
            BaseNodeCanvas originalNext = processNode.getFlowNodeMap().get(originalNextId);
            if (originalNext instanceof IRoutable routableNext) {
                routableNext.setPrveId(gateway.getId());
                nodeBatchExecutor.updateNode(originalNextId, originalNext);
            }
            log.debug("不移动策略：网关 {} 合流到 {}", gateway.getId(), originalNextId);
        }

        // 两个分支都设为空分支（等待后续添加内容）
        leftBranch.setNextId("");
        rightBranch.setNextId("");
    }



    /**
     * 删除网关
     * @param gatewayId 网关ID
     */
    public void deleteGateway(String gatewayId) {
        if (nodeBatchExecutor == null || gatewayId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            throw new IllegalStateException("网关没有分支，无法删除");
        }

        if (flowIds.size() > 1) {
            throw new IllegalStateException("网关有多个分支，请先删除分支至只剩1条");
        }

        // 只剩一条分支，执行扁平化
        String remainingBranchId = flowIds.get(0);
        flattenGateway(gateway, remainingBranchId);

        nodeBatchExecutor.deleteNode(gatewayId);

        log.debug("删除网关 {}，已扁平化", gatewayId);
    }

    /**
     * 扁平化网关（将单分支网关替换为分支链）
     * 优化后使用AutoWireStrategy.replace原语，遵循设计方案
     */
    private void flattenGateway(GatewayNodeCanvas gateway, String branchLeafId) {
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();

        log.debug("扁平化网关 {}，分支叶子: {}", gateway.getId(), branchLeafId);

        // 获取分支链
        List<BaseNodeCanvas> branchChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);

        if (branchChain.isEmpty()) {
            // 分支为空，使用AutoWireStrategy.detach断开网关连接
            // 这相当于删除网关，让前驱直接连接到网关的下游
            autoWireStrategy.detach(workingCopy, gateway, gateway);
            log.debug("空分支扁平化：网关 {} 被移除，前驱直接连接到后继", gateway.getId());
        } else {
            // 分支不为空，使用AutoWireStrategy.replace将网关替换为分支链
            BaseNodeCanvas branchHead = branchChain.get(0);
            BaseNodeCanvas branchTail = branchChain.get(branchChain.size() - 1);

            // 先清除分支叶子节点的prveId（因为它指向即将被删除的网关）
            if (branchHead instanceof IRoutable routableHead) {
                routableHead.setPrveId("");
            }

            autoWireStrategy.replace(workingCopy, gateway, gateway, branchHead, branchTail);

            // 更新分支链中所有节点到批处理器
            for (BaseNodeCanvas chainNode : branchChain) {
                nodeBatchExecutor.updateNode(chainNode.getId(), chainNode);
            }

            log.debug("非空分支扁平化：网关 {} 被分支链 {} -> {} 替换",
                     gateway.getId(), branchHead.getId(), branchTail.getId());
        }

        log.debug("网关 {} 扁平化完成", gateway.getId());
    }



    /**
     * 修改网关类型
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(String gatewayId, Integer toType) {
        if (nodeBatchExecutor == null || gatewayId == null || toType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        // 使用网关语义策略处理类型切换
        GatewaySemanticsStrategy.switchGatewayType(workingCopy, gateway, toType);

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 更新所有被修改的分支节点
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchNode = workingCopy.getFlowNodeMap().get(flowId);
                if (branchNode != null) {
                    nodeBatchExecutor.updateNode(flowId, branchNode);
                    log.debug("更新分支节点: {}", flowId);
                }
            }
        }

        log.debug("网关 {} 类型已切换为 {}", gatewayId, toType);
    }

    /**
     * 为分支设置默认条件
     *
     * @param branch 分支节点
     * @param index 分支索引
     */
    private void setDefaultConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");

            if (index == 2 - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
