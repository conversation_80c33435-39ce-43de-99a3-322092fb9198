package com.mlc.workflow.service.landing.dto;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 节点操作结果DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@Setter
@DataBean
public class NodeOperationResult {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * Canvas节点数据
     */
    private BaseNodeCanvas canvasNode;
    
    /**
     * Properties节点数据
     */
    private BaseNodeProperties propertiesNode;
    
    /**
     * 流程处理结果JSON
     */
    private String processResultJson;
    
    /**
     * 节点特定逻辑查询结果
     */
    private Map<String, Object> specificQueryResult;
    
    /**
     * 组装后的完整数据
     */
    private Map<String, Object> assembledData;
    
    /**
     * 创建成功结果
     */
    public static NodeOperationResult success() {
        NodeOperationResult result = new NodeOperationResult();
        result.setSuccess(true);
        return result;
    }
    
    /**
     * 创建失败结果
     */
    public static NodeOperationResult failure(String errorMessage) {
        NodeOperationResult result = new NodeOperationResult();
        result.setSuccess(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
