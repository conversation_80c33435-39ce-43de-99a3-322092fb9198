package com.mlc.workflow.service.landing.factory;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.core.editor.model.properties.*;
import io.nop.core.context.IServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 节点Properties工厂类
 * 根据Canvas节点创建对应的Properties节点
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
public class NodePropertiesFactory {
    
    /**
     * 根据Canvas节点创建对应的Properties节点
     * 
     * @param canvas Canvas节点
     * @param context 服务上下文
     * @return Properties节点
     */
    public BaseNodeProperties createPropertiesNode(BaseNodeCanvas canvas, IServiceContext context) {
        if (canvas == null) {
            throw new IllegalArgumentException("Canvas节点不能为空");
        }
        
        NodeTypeEnum nodeType = canvas.getNodeType();
        if (nodeType == null) {
            throw new IllegalArgumentException("节点类型不能为空");
        }
        
        log.info("开始创建Properties节点，节点类型: {}, 节点ID: {}", nodeType, canvas.getId());
        
        BaseNodeProperties properties = createByNodeType(nodeType, canvas, context);
        
        // 设置通用属性
        setCommonProperties(properties, canvas);
        
        log.info("Properties节点创建完成，节点类型: {}, 节点ID: {}", nodeType, canvas.getId());
        
        return properties;
    }
    
    /**
     * 根据节点类型创建具体的Properties对象
     */
    private BaseNodeProperties createByNodeType(NodeTypeEnum nodeType, BaseNodeCanvas canvas, IServiceContext context) {
        switch (nodeType) {
            case START:
                return new StartEventNodeProperties();
            case BRANCH:
                return new BranchNodeProperties();
            case BRANCH_ITEM:
                return new ConditionNodeProperties();
            case WRITE:
                return new WriteNodeProperties();
            case APPROVAL:
                return new ApprovalNodeProperties();
            case CC:
                return new CcNodeProperties();
            case ACTION:
                return new ActionNodeProperties();
            case SEARCH:
                return new SearchNodeProperties();
            case WEBHOOK:
                return new WebhookNodeProperties();
            case FORMULA:
                return new FormulaNodeProperties();
            case MESSAGE:
                return new MessageNodeProperties();
            case EMAIL:
                return new EmailNodeProperties();
            case DELAY:
                return new DelayNodeProperties();
            case GET_MORE_RECORD:
                return new GetMoreRecordNodeProperties();
            case CODE:
                return new CodeNodeProperties();
            case LINK:
                return new LinkNodeProperties();
            case SUB_PROCESS:
                return new SubProcessNodeProperties();
            case PUSH:
                return new PushNodeProperties();
            case FILE:
                return new FileNodeProperties();
            case TEMPLATE:
                return new TemplateNodeProperties();
            case PBC:
                return new PbcNodeProperties();
            case JSON_PARSE:
                return new JsonParseNodeProperties();
            case AUTHENTICATION:
                return new AuthenticationNodeProperties();
            case PARAMETER:
                return new ParameterNodeProperties();
            case API_PACKAGE:
                return new ApiPackageNodeProperties();
            case API:
                return new ApiNodeProperties();
            case APPROVAL_PROCESS:
                return new ApprovalProcessNodeProperties();
            case NOTICE:
                return new NotifyNodeProperties();
            case SNAPSHOT:
                return new SnapshotNodeProperties();
            case LOOP:
                return new LoopNodeProperties();
            case RETURN:
                return new ReturnNodeProperties();
            case AIGC:
                return new AigcNodeProperties();
            case PLUGIN:
                return new PluginNodeProperties();
            case SYSTEM:
                return new SystemNodeProperties();
            case FIND_SINGLE_MESSAGE:
                return new FindSingleMessageNodeProperties();
            case FIND_MORE_MESSAGE:
                return new FindMoreMessageNodeProperties();
            default:
                throw new IllegalArgumentException("不支持的节点类型: " + nodeType);
        }
    }
    
    /**
     * 设置通用属性
     */
    private void setCommonProperties(BaseNodeProperties properties, BaseNodeCanvas canvas) {
        properties.setNodeId(canvas.getId());
        properties.setFlowNodeType(canvas.getTypeId());
        properties.setName(canvas.getName());
        
        // 这里可以设置更多通用属性
        // 例如：从Canvas中提取一些通用信息设置到Properties中
    }
}
