package com.mlc.workflow.service.landing.processor;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.core.context.IServiceContext;

/**
 * 节点处理器接口
 * 使用策略模式处理不同类型的节点
 * 
 * <AUTHOR>
 * @since 1.0
 */
public interface INodeProcessor {
    
    /**
     * 判断是否支持处理指定类型的节点
     * 
     * @param nodeType 节点类型
     * @return 是否支持
     */
    boolean supports(Integer nodeType);
    
    /**
     * 处理Canvas节点
     * 调用现有流程处理程序，返回JSON结果
     * 
     * @param canvas 画布节点
     * @param context 服务上下文
     * @return 处理结果JSON字符串
     */
    String processCanvas(BaseNodeCanvas canvas, IServiceContext context);
    
    /**
     * 获取支持的节点类型
     * 
     * @return 节点类型
     */
    Integer getSupportedNodeType();
}
