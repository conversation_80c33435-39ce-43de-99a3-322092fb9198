package com.mlc.workflow.service.landing.processor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import io.nop.core.context.IServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 节点处理器抽象基类
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public abstract class AbstractNodeProcessor implements INodeProcessor {
    
    @Autowired
    protected ObjectMapper objectMapper;
    
    @Override
    public boolean supports(Integer nodeType) {
        return getSupportedNodeType().equals(nodeType);
    }
    
    @Override
    public String processCanvas(BaseNodeCanvas canvas, IServiceContext context) {
        try {
            log.info("开始处理Canvas节点，节点类型: {}, 节点ID: {}", canvas.getTypeId(), canvas.getId());
            
            // 验证节点
            validateCanvas(canvas);
            
            // 预处理
            preProcess(canvas, context);
            
            // 执行具体处理逻辑
            String result = doProcessCanvas(canvas, context);
            
            // 后处理
            postProcess(canvas, result, context);
            
            log.info("Canvas节点处理完成，节点ID: {}", canvas.getId());
            return result;
            
        } catch (Exception e) {
            log.error("Canvas节点处理失败，节点ID: " + canvas.getId(), e);
            throw new RuntimeException("Canvas节点处理失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证Canvas节点
     */
    protected void validateCanvas(BaseNodeCanvas canvas) {
        if (canvas == null) {
            throw new IllegalArgumentException("Canvas节点不能为空");
        }
        if (!canvas.isValid()) {
            throw new IllegalArgumentException("Canvas节点数据无效");
        }
    }
    
    /**
     * 预处理
     */
    protected void preProcess(BaseNodeCanvas canvas, IServiceContext context) {
        // 子类可重写
    }
    
    /**
     * 执行具体的Canvas处理逻辑
     * 子类必须实现
     */
    protected abstract String doProcessCanvas(BaseNodeCanvas canvas, IServiceContext context);
    
    /**
     * 后处理
     */
    protected void postProcess(BaseNodeCanvas canvas, String result, IServiceContext context) {
        // 子类可重写
    }
    
    /**
     * 将对象转换为JSON字符串
     */
    protected String toJsonString(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            log.error("对象转JSON失败", e);
            throw new RuntimeException("对象转JSON失败", e);
        }
    }
}
