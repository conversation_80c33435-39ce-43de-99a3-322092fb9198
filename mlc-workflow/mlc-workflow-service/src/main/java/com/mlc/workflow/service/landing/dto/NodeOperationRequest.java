package com.mlc.workflow.service.landing.dto;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 节点操作请求DTO
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Getter
@Setter
@DataBean
public class NodeOperationRequest {
    
    /**
     * 流程ID
     */
    private String processId;
    
    /**
     * 节点ID
     */
    private String nodeId;
    
    /**
     * 节点类型
     */
    private Integer nodeType;
    
    /**
     * Canvas节点数据
     */
    private BaseNodeCanvas canvasNode;
    
    /**
     * Properties节点数据
     */
    private BaseNodeProperties propertiesNode;
    
    /**
     * 扩展参数
     */
    private Object additionalParams;
}
