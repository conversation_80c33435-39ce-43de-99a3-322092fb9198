package com.mlc.workflow.service.landing.query.components;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.query.AbstractQueryComponent;
import io.nop.core.context.IServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 通用数据查询组件
 * 处理大部分节点类型的通用查询逻辑
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
@Component
public class CommonDataQueryComponent extends AbstractQueryComponent {
    
    private static final Set<Integer> SUPPORTED_NODE_TYPES = Set.of(
            NodeTypeEnum.WRITE.getValue(),
            NodeTypeEnum.ACTION.getValue(),
            NodeTypeEnum.SEARCH.getValue(),
            NodeTypeEnum.FORMULA.getValue(),
            NodeTypeEnum.CODE.getValue(),
            NodeTypeEnum.LINK.getValue(),
            NodeTypeEnum.FILE.getValue(),
            NodeTypeEnum.JSON_PARSE.getValue(),
            NodeTypeEnum.PARAMETER.getValue()
    );
    
    @Override
    public Set<Integer> getSupportedNodeTypes() {
        return SUPPORTED_NODE_TYPES;
    }
    
    @Override
    public int getPriority() {
        return 50; // 中等优先级
    }
    
    @Override
    protected Map<String, Object> doExecuteQuery(NodeOperationRequest request, IServiceContext context) {
        Map<String, Object> result = new HashMap<>();
        
        // 查询基础配置信息
        result.put("baseConfig", queryBaseConfig(request, context));
        
        // 查询关联数据
        result.put("relatedData", queryRelatedData(request, context));
        
        // 查询权限信息
        result.put("permissions", queryPermissions(request, context));
        
        return result;
    }
    
    /**
     * 查询基础配置信息
     */
    private Map<String, Object> queryBaseConfig(NodeOperationRequest request, IServiceContext context) {
        Map<String, Object> config = new HashMap<>();
        config.put("nodeId", request.getNodeId());
        config.put("nodeType", request.getNodeType());
        config.put("processId", request.getProcessId());
        
        // 这里可以添加更多基础配置查询逻辑
        log.debug("查询基础配置完成，节点ID: {}", request.getNodeId());
        
        return config;
    }
    
    /**
     * 查询关联数据
     */
    private Map<String, Object> queryRelatedData(NodeOperationRequest request, IServiceContext context) {
        Map<String, Object> relatedData = new HashMap<>();
        
        // 这里可以添加关联数据查询逻辑
        // 例如：查询相关的工作表、字段、用户等
        
        log.debug("查询关联数据完成，节点ID: {}", request.getNodeId());
        
        return relatedData;
    }
    
    /**
     * 查询权限信息
     */
    private Map<String, Object> queryPermissions(NodeOperationRequest request, IServiceContext context) {
        Map<String, Object> permissions = new HashMap<>();
        
        // 这里可以添加权限查询逻辑
        permissions.put("canEdit", true);
        permissions.put("canDelete", true);
        permissions.put("canView", true);
        
        log.debug("查询权限信息完成，节点ID: {}", request.getNodeId());
        
        return permissions;
    }
}
