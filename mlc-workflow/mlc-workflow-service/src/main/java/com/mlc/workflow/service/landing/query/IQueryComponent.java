package com.mlc.workflow.service.landing.query;

import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import io.nop.core.context.IServiceContext;

import java.util.Map;

/**
 * 查询组件接口
 * 用于执行节点特定的逻辑查询，支持40种节点类型的复用逻辑
 * 
 * <AUTHOR>
 * @since 1.0
 */
public interface IQueryComponent {
    
    /**
     * 判断是否支持指定的节点类型
     * 
     * @param nodeType 节点类型
     * @return 是否支持
     */
    boolean supports(Integer nodeType);
    
    /**
     * 执行查询
     * 
     * @param request 节点操作请求
     * @param context 服务上下文
     * @return 查询结果
     */
    Map<String, Object> executeQuery(NodeOperationRequest request, IServiceContext context);
    
    /**
     * 获取支持的节点类型
     * 
     * @return 节点类型集合
     */
    java.util.Set<Integer> getSupportedNodeTypes();
    
    /**
     * 获取查询组件的优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
}
