package com.mlc.workflow.service.landing.core;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.BaseNodeProperties;
import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import com.mlc.workflow.service.landing.dto.NodeOperationResult;
import com.mlc.workflow.service.landing.processor.INodeProcessor;
import com.mlc.workflow.service.landing.processor.NodeProcessorFactory;
import com.mlc.workflow.service.landing.query.IQueryComponent;
import com.mlc.workflow.service.landing.query.QueryComponentRegistry;
import io.nop.core.context.IServiceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 节点操作服务抽象基类
 * 使用模板方法模式定义操作流程，具体实现由子类完成
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public abstract class AbstractNodeOperationService implements INodeOperationService {
    
    @Autowired
    protected NodeProcessorFactory processorFactory;
    
    @Autowired
    protected QueryComponentRegistry queryComponentRegistry;
    
    @Override
    public NodeOperationResult initializeNode(BaseNodeCanvas canvas, IServiceContext context) {
        try {
            log.info("开始初始化节点，节点ID: {}, 节点类型: {}", canvas.getId(), canvas.getTypeId());
            
            // 步骤1：处理Canvas节点
            String processResultJson = processCanvasNode(canvas, context);
            
            // 步骤2：创建Properties节点
            BaseNodeProperties properties = createPropertiesNode(canvas, context);
            
            // 保存Properties节点
            savePropertiesNode(properties, context);
            
            NodeOperationResult result = NodeOperationResult.success();
            result.setCanvasNode(canvas);
            result.setPropertiesNode(properties);
            result.setProcessResultJson(processResultJson);
            
            log.info("节点初始化完成，节点ID: {}", canvas.getId());
            return result;
            
        } catch (Exception e) {
            log.error("节点初始化失败，节点ID: " + canvas.getId(), e);
            return NodeOperationResult.failure("节点初始化失败: " + e.getMessage());
        }
    }
    
    @Override
    public NodeOperationResult addNode(NodeOperationRequest request, IServiceContext context) {
        try {
            log.info("开始添加节点，节点ID: {}", request.getNodeId());
            
            // 步骤1：处理Canvas节点
            String processResultJson = processCanvasNode(request.getCanvasNode(), context);
            
            // 步骤2：创建Properties节点
            BaseNodeProperties properties = createPropertiesNode(request.getCanvasNode(), context);
            savePropertiesNode(properties, context);
            
            // 步骤3：执行节点特定逻辑查询
            Map<String, Object> specificResult = executeSpecificQuery(request, context);
            
            // 组装数据
            Map<String, Object> assembledData = assembleData(properties, specificResult, context);
            
            NodeOperationResult result = NodeOperationResult.success();
            result.setCanvasNode(request.getCanvasNode());
            result.setPropertiesNode(properties);
            result.setProcessResultJson(processResultJson);
            result.setSpecificQueryResult(specificResult);
            result.setAssembledData(assembledData);
            
            log.info("节点添加完成，节点ID: {}", request.getNodeId());
            return result;
            
        } catch (Exception e) {
            log.error("节点添加失败，节点ID: " + request.getNodeId(), e);
            return NodeOperationResult.failure("节点添加失败: " + e.getMessage());
        }
    }
    
    @Override
    public NodeOperationResult deleteNode(NodeOperationRequest request, IServiceContext context) {
        try {
            log.info("开始删除节点，节点ID: {}", request.getNodeId());
            
            // 步骤1：处理Canvas节点（更新状态）
            String processResultJson = processCanvasNode(request.getCanvasNode(), context);
            
            // 步骤2：删除Properties节点
            deletePropertiesNode(request.getNodeId(), context);
            
            // 步骤3：执行节点特定逻辑查询
            Map<String, Object> specificResult = executeSpecificQuery(request, context);
            
            // 组装数据
            Map<String, Object> assembledData = assembleData(null, specificResult, context);
            
            NodeOperationResult result = NodeOperationResult.success();
            result.setProcessResultJson(processResultJson);
            result.setSpecificQueryResult(specificResult);
            result.setAssembledData(assembledData);
            
            log.info("节点删除完成，节点ID: {}", request.getNodeId());
            return result;
            
        } catch (Exception e) {
            log.error("节点删除失败，节点ID: " + request.getNodeId(), e);
            return NodeOperationResult.failure("节点删除失败: " + e.getMessage());
        }
    }
    
    @Override
    public NodeOperationResult updateNode(NodeOperationRequest request, IServiceContext context) {
        try {
            log.info("开始更新节点，节点ID: {}", request.getNodeId());
            
            // 步骤1：处理Canvas节点
            String processResultJson = processCanvasNode(request.getCanvasNode(), context);
            
            // 步骤2：更新Properties节点
            updatePropertiesNode(request.getPropertiesNode(), context);
            
            // 步骤3：执行节点特定逻辑查询
            Map<String, Object> specificResult = executeSpecificQuery(request, context);
            
            // 组装数据
            Map<String, Object> assembledData = assembleData(request.getPropertiesNode(), specificResult, context);
            
            NodeOperationResult result = NodeOperationResult.success();
            result.setCanvasNode(request.getCanvasNode());
            result.setPropertiesNode(request.getPropertiesNode());
            result.setProcessResultJson(processResultJson);
            result.setSpecificQueryResult(specificResult);
            result.setAssembledData(assembledData);
            
            log.info("节点更新完成，节点ID: {}", request.getNodeId());
            return result;
            
        } catch (Exception e) {
            log.error("节点更新失败，节点ID: " + request.getNodeId(), e);
            return NodeOperationResult.failure("节点更新失败: " + e.getMessage());
        }
    }
    
    @Override
    public NodeOperationResult queryPropertiesNode(NodeOperationRequest request, IServiceContext context) {
        try {
            log.info("开始查询Properties节点，节点ID: {}", request.getNodeId());
            
            // 步骤1：查询基础节点数据
            BaseNodeProperties properties = queryBasePropertiesNode(request.getNodeId(), context);
            
            // 步骤2：执行节点特定逻辑查询
            Map<String, Object> specificResult = executeSpecificQuery(request, context);
            
            // 步骤3：组装数据
            Map<String, Object> assembledData = assembleData(properties, specificResult, context);
            
            NodeOperationResult result = NodeOperationResult.success();
            result.setPropertiesNode(properties);
            result.setSpecificQueryResult(specificResult);
            result.setAssembledData(assembledData);
            
            log.info("Properties节点查询完成，节点ID: {}", request.getNodeId());
            return result;
            
        } catch (Exception e) {
            log.error("Properties节点查询失败，节点ID: " + request.getNodeId(), e);
            return NodeOperationResult.failure("Properties节点查询失败: " + e.getMessage());
        }
    }
    
    // ==================== 模板方法 ====================
    
    /**
     * 处理Canvas节点
     * 调用现有流程处理程序，返回JSON结果并入库
     */
    protected String processCanvasNode(BaseNodeCanvas canvas, IServiceContext context) {
        INodeProcessor processor = processorFactory.getProcessor(canvas.getTypeId());
        return processor.processCanvas(canvas, context);
    }
    
    /**
     * 创建Properties节点
     * 基于Canvas节点创建对应的Properties节点
     */
    protected abstract BaseNodeProperties createPropertiesNode(BaseNodeCanvas canvas, IServiceContext context);
    
    /**
     * 保存Properties节点
     */
    protected abstract void savePropertiesNode(BaseNodeProperties properties, IServiceContext context);
    
    /**
     * 删除Properties节点
     */
    protected abstract void deletePropertiesNode(String nodeId, IServiceContext context);
    
    /**
     * 更新Properties节点
     */
    protected abstract void updatePropertiesNode(BaseNodeProperties properties, IServiceContext context);
    
    /**
     * 查询基础Properties节点数据
     */
    protected abstract BaseNodeProperties queryBasePropertiesNode(String nodeId, IServiceContext context);
    
    /**
     * 执行节点特定逻辑查询
     */
    protected Map<String, Object> executeSpecificQuery(NodeOperationRequest request, IServiceContext context) {
        IQueryComponent queryComponent = queryComponentRegistry.getQueryComponent(request.getNodeType());
        return queryComponent.executeQuery(request, context);
    }
    
    /**
     * 组装数据
     * 将基础数据与特定逻辑数据组装后返回
     */
    protected abstract Map<String, Object> assembleData(BaseNodeProperties properties, 
                                                       Map<String, Object> specificResult, 
                                                       IServiceContext context);
}
