package com.mlc.workflow.service.landing.query;

import com.mlc.workflow.service.landing.dto.NodeOperationRequest;
import io.nop.core.context.IServiceContext;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询组件抽象基类
 * 提供通用的查询逻辑和模板方法
 * 
 * <AUTHOR>
 * @since 1.0
 */
@Slf4j
public abstract class AbstractQueryComponent implements IQueryComponent {
    
    @Override
    public boolean supports(Integer nodeType) {
        return getSupportedNodeTypes().contains(nodeType);
    }
    
    @Override
    public Map<String, Object> executeQuery(NodeOperationRequest request, IServiceContext context) {
        try {
            log.info("开始执行查询，节点类型: {}, 节点ID: {}", request.getNodeType(), request.getNodeId());
            
            // 验证请求
            validateRequest(request);
            
            // 预处理
            preQuery(request, context);
            
            // 执行具体查询
            Map<String, Object> result = doExecuteQuery(request, context);
            
            // 后处理
            result = postQuery(result, request, context);
            
            log.info("查询执行完成，节点ID: {}, 结果数量: {}", request.getNodeId(), result.size());
            return result;
            
        } catch (Exception e) {
            log.error("查询执行失败，节点ID: " + request.getNodeId(), e);
            throw new RuntimeException("查询执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 验证请求参数
     */
    protected void validateRequest(NodeOperationRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("查询请求不能为空");
        }
        if (request.getNodeType() == null) {
            throw new IllegalArgumentException("节点类型不能为空");
        }
        if (!supports(request.getNodeType())) {
            throw new IllegalArgumentException("不支持的节点类型: " + request.getNodeType());
        }
    }
    
    /**
     * 预处理
     */
    protected void preQuery(NodeOperationRequest request, IServiceContext context) {
        // 子类可重写
    }
    
    /**
     * 执行具体的查询逻辑
     * 子类必须实现
     */
    protected abstract Map<String, Object> doExecuteQuery(NodeOperationRequest request, IServiceContext context);
    
    /**
     * 后处理
     */
    protected Map<String, Object> postQuery(Map<String, Object> result, NodeOperationRequest request, IServiceContext context) {
        // 子类可重写
        return result != null ? result : new HashMap<>();
    }
}
